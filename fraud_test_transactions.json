{"description": "High-risk fraud test transactions for dashboard testing", "generated_at": "2025-05-25T16:30:00Z", "model_info": "Based on real fraud patterns from training data", "high_risk_transactions": [{"transaction_id": "high_risk_001_transfer_empty_account", "step": 1, "type": "TRANSFER", "amount": 2500.0, "nameOrig": "C123456789", "oldbalanceOrg": 2500.0, "newbalanceOrig": 0.0, "nameDest": "C987654321", "oldbalanceDest": 10000.0, "newbalanceDest": 12500.0, "description": "Complete account emptying via transfer - matches training fraud pattern"}, {"transaction_id": "high_risk_002_cashout_dest_zero", "step": 1, "type": "CASH_OUT", "amount": 1900.0, "nameOrig": "C555666777", "oldbalanceOrg": 2200.0, "newbalanceOrig": 300.0, "nameDest": "C444555666", "oldbalanceDest": 1000.0, "newbalanceDest": 0.0, "description": "Cash out with destination balance going to zero - suspicious pattern"}, {"transaction_id": "high_risk_003_large_transfer_empty", "step": 1, "type": "TRANSFER", "amount": 50000.0, "nameOrig": "C888999000", "oldbalanceOrg": 50000.0, "newbalanceOrig": 0.0, "nameDest": "C111222333", "oldbalanceDest": 0.0, "newbalanceDest": 50000.0, "description": "Large amount transfer with complete account emptying"}, {"transaction_id": "high_risk_004_cashout_suspicious", "step": 1, "type": "CASH_OUT", "amount": 3000.0, "nameOrig": "C777888999", "oldbalanceOrg": 4000.0, "newbalanceOrig": 1000.0, "nameDest": "C333444555", "oldbalanceDest": 5000.0, "newbalanceDest": 2000.0, "description": "Cash out where destination loses money - very suspicious"}, {"transaction_id": "high_risk_005_small_transfer_empty", "step": 1, "type": "TRANSFER", "amount": 1200.0, "nameOrig": "C111000111", "oldbalanceOrg": 1200.0, "newbalanceOrig": 0.0, "nameDest": "C222000222", "oldbalanceDest": 0.0, "newbalanceDest": 1200.0, "description": "Small transfer with complete account emptying"}, {"transaction_id": "high_risk_006_large_amount_fraud", "step": 1, "type": "TRANSFER", "amount": 100000.0, "nameOrig": "C999888777", "oldbalanceOrg": 100000.0, "newbalanceOrig": 0.0, "nameDest": "C666555444", "oldbalanceDest": 0.0, "newbalanceDest": 100000.0, "description": "Large amount with proven fraud pattern"}, {"transaction_id": "high_risk_007_extreme_cashout", "step": 1, "type": "CASH_OUT", "amount": 500000.0, "nameOrig": "C123000123", "oldbalanceOrg": 500000.0, "newbalanceOrig": 0.0, "nameDest": "M999000999", "oldbalanceDest": 0.0, "newbalanceDest": 500000.0, "description": "Extreme amount cash out with account emptying to merchant"}, {"transaction_id": "high_risk_008_balance_mismatch", "step": 1, "type": "TRANSFER", "amount": 75000.0, "nameOrig": "C456000456", "oldbalanceOrg": 80000.0, "newbalanceOrig": 5000.0, "nameDest": "C789000789", "oldbalanceDest": 20000.0, "newbalanceDest": 90000.0, "description": "Transfer with suspicious balance calculations"}], "medium_risk_transactions": [{"transaction_id": "medium_risk_001_large_payment", "step": 1, "type": "PAYMENT", "amount": 80000.0, "nameOrig": "C333444555", "oldbalanceOrg": 100000.0, "newbalanceOrig": 20000.0, "nameDest": "M333444555", "oldbalanceDest": 0.0, "newbalanceDest": 0.0, "description": "Large payment to merchant - legitimate but high amount"}, {"transaction_id": "medium_risk_002_moderate_transfer", "step": 1, "type": "TRANSFER", "amount": 25000.0, "nameOrig": "C666777888", "oldbalanceOrg": 50000.0, "newbalanceOrig": 25000.0, "nameDest": "C999000111", "oldbalanceDest": 10000.0, "newbalanceDest": 35000.0, "description": "Moderate transfer - 50% of balance but not emptying"}, {"transaction_id": "medium_risk_003_large_debit", "step": 1, "type": "DEBIT", "amount": 15000.0, "nameOrig": "C111222333", "oldbalanceOrg": 30000.0, "newbalanceOrig": 15000.0, "nameDest": "C444555666", "oldbalanceDest": 5000.0, "newbalanceDest": 20000.0, "description": "Large debit transaction - 50% of balance"}], "low_risk_transactions": [{"transaction_id": "low_risk_001_small_payment", "step": 1, "type": "PAYMENT", "amount": 500.0, "nameOrig": "C111222333", "oldbalanceOrg": 5000.0, "newbalanceOrig": 4500.0, "nameDest": "M111222333", "oldbalanceDest": 0.0, "newbalanceDest": 0.0, "description": "Small payment to merchant - typical legitimate transaction"}, {"transaction_id": "low_risk_002_small_transfer", "step": 1, "type": "TRANSFER", "amount": 1000.0, "nameOrig": "C444555666", "oldbalanceOrg": 10000.0, "newbalanceOrig": 9000.0, "nameDest": "C777888999", "oldbalanceDest": 2000.0, "newbalanceDest": 3000.0, "description": "Small transfer - 10% of balance, normal pattern"}, {"transaction_id": "low_risk_003_small_debit", "step": 1, "type": "DEBIT", "amount": 200.0, "nameOrig": "C999000111", "oldbalanceOrg": 2000.0, "newbalanceOrig": 1800.0, "nameDest": "C222333444", "oldbalanceDest": 1000.0, "newbalanceDest": 1200.0, "description": "Small debit - 10% of balance, normal pattern"}], "dashboard_instructions": {"how_to_use": ["1. Copy any transaction object from above", "2. Paste the values into the HTML dashboard form fields", "3. <PERSON><PERSON> 'Check for Fraud' to see the risk assessment", "4. Compare results across different risk levels"], "expected_results": {"high_risk": "Should produce risk scores above 50% (though current model may be conservative)", "medium_risk": "Should produce moderate risk scores (20-50%)", "low_risk": "Should produce low risk scores (0-20%)"}, "testing_tips": ["Try the 'Random Transaction' button to generate test data", "Use the transaction history to compare different patterns", "Test with different transaction types to see model behavior", "Pay attention to account emptying patterns (oldbalanceOrg = amount, newbalanceOrig = 0)"]}, "api_format": {"description": "Format for direct API testing", "endpoint": "POST http://localhost:8001/score", "request_format": {"transactions": [{"transaction_id": "test_001", "step": 1, "type": "TRANSFER", "amount": 2500.0, "nameOrig": "C123456789", "oldbalanceOrg": 2500.0, "newbalanceOrig": 0.0, "nameDest": "C987654321", "oldbalanceDest": 10000.0, "newbalanceDest": 12500.0}]}, "response_format": {"results": [{"transaction_id": "test_001", "risk": 0.101}]}}}